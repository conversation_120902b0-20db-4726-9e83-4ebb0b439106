import logging
import os
import urllib.parse

from dotenv import load_dotenv
from langchain_community.utilities.sql_database import SQLDatabase

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MySQLConnection:
    """
    MySQL Database Connection Utility Class

    This class provides methods to connect to MySQL database,
    execute queries, and manage database connections safely.
    """

    def __init__(self):
        """Initialize MySQL connection parameters from environment variables"""
        self.host = os.getenv("MYSQL_HOST", "localhost")
        self.port = os.getenv("MYSQL_PORT", 3306)
        self.user = os.getenv("MYSQL_USER", "root")
        self.password = os.getenv("MYSQL_PASSWORD", "")
        self.database = os.getenv("MYSQL_DATABASE", "")

    def get_database_connection(self):
        """
        Create and return a LangChain SQLDatabase instance using our MySQL connection

        Returns:
            SQLDatabase: LangChain <PERSON>QL<PERSON>base instance
        """

        # URL-encode user and password
        encoded_user = urllib.parse.quote_plus(self.user)
        encoded_password = urllib.parse.quote_plus(self.password)

        # Create MySQL connection URI for LangChain
        mysql_uri = f"mysql+pymysql://{encoded_user}:{encoded_password}@{self.host}:{self.port}/{self.database}"

        try:
            # Create SQLDatabase instance
            db = SQLDatabase.from_uri(mysql_uri)
            print(f"✅ Successfully connected to MySQL database: {self.database}")
            return db
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")


if __name__ == "__main__":
    db_conn = MySQLConnection()
    db = db_conn.get_database_connection()
    print(db)
